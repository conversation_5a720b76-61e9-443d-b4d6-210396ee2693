package com.inossem.wms.bizdomain.supplier.service.component;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.inossem.wms.bizbasis.common.service.biz.*;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicDeptDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysRoleDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserRoleRelDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.HXSapIntegerfaceService;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractHeadDataWrap;
import com.inossem.wms.bizdomain.srm.service.SrmService;
import com.inossem.wms.bizdomain.supplier.service.datawrap.DicSupplierDataWrap;
import com.inossem.wms.bizdomain.supplier.service.datawrap.DicSupplierUserRelDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.auth.EnumUserJob;
import com.inossem.wms.common.enums.auth.EnumUserType;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.supplier.*;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.approval.dto.RevokeDTO;
import com.inossem.wms.common.model.auth.rel.entity.SysUserDeptOfficeRel;
import com.inossem.wms.common.model.auth.rel.entity.SysUserRoleRel;
import com.inossem.wms.common.model.auth.role.entity.SysRole;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractHead;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.common.enums.supplier.ScopeMapVO;
import com.inossem.wms.common.model.common.enums.supplier.SupplierAttrMapVO;
import com.inossem.wms.common.model.common.enums.supplier.SupplierClassMapVO;
import com.inossem.wms.common.model.common.enums.supplier.SupplierTypeMapVO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.masterdata.base.entity.DicDept;
import com.inossem.wms.common.model.masterdata.supplier.dto.DicSupplierDTO;
import com.inossem.wms.common.model.masterdata.supplier.dto.DicSupplierExcelDTO;
import com.inossem.wms.common.model.masterdata.supplier.dto.DicSupplierUserRelDTO;
import com.inossem.wms.common.model.masterdata.supplier.entity.DicSupplier;
import com.inossem.wms.common.model.masterdata.supplier.entity.DicSupplierUserRel;
import com.inossem.wms.common.model.masterdata.supplier.po.DicSupplierPO;
import com.inossem.wms.common.model.masterdata.supplier.vo.DicSupplierVO;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import com.inossem.wms.common.util.excel.UtilExcel;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 供应商管理
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Service
@Slf4j
public class SupplierComponent {

    @Autowired
    protected HXSapIntegerfaceService hXSapIntegerfaceService;
    @Autowired
    protected I18nTextCommonService i18nTextCommonService;
    @Autowired
    protected BizCommonService bizCommonService;
    @Autowired
    private EditCacheService editCacheService;
    @Autowired
    protected ApprovalService approvalService;
    @Autowired
    protected DataFillService dataFillService;
    @Autowired
    protected WorkflowService workflowService;
    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;
    @Autowired
    protected DicSupplierDataWrap dicSupplierDataWrap;
    @Autowired
    protected DicSupplierUserRelDataWrap dicSupplierUserRelDataWrap;
    @Autowired
    protected SysUserDataWrap sysUserDataWrap;
    @Autowired
    protected SysUserRoleRelDataWrap sysUserRoleRelDataWrap;
    @Autowired
    protected SysRoleDataWrap sysRoleDataWrap;
    @Autowired
    private DicDeptDataWrap dicDeptDataWrap;
    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;
    @Autowired
    protected BizReceiptContractHeadDataWrap bizReceiptContractHeadDataWrap;
    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private SrmService srmService;

    @Value("${wms.srm.syncSupplier-url}")
    private String syncSupplierUrl;

    /**
     * 查询供应商类型下拉
     */
    public void supplierType(BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(EnumSupplierType.toList()));
    }

    /**
     * 查询供应商分类下拉
     */
    public void supplierClass(BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(EnumSupplierClass.toList()));
    }

    /**
     * 查询供应商属性下拉
     */
    public void supplierAttr(BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(EnumSupplierAttr.toList()));
    }

    /**
     * 查询物料等级下拉
     */
    public void matLevel(BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(EnumMatLevel.toList()));
    }

    /**
     * 查询业务范围下拉
     */
    public void scope(BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(EnumScope.toList()));
    }

    /**
     * 分页
     */
    public void getPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        DicSupplierPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(UtilCollection.isEmpty(po.getReceiptStatusList())){
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(new ArrayList<>(), 0L));
            return;
        }
        // 分页查询处理
        IPage<DicSupplier> page = po.getPageObj(DicSupplier.class);
        QueryWrapper<DicSupplier> queryWrapper = new QueryWrapper();
        queryWrapper.lambda()
                .eq(UtilString.isNotNullOrEmpty(po.getSupplierCode()), DicSupplier::getSupplierCode, po.getSupplierCode())
                .like(UtilString.isNotNullOrEmpty(po.getSupplierName()), DicSupplier::getSupplierName, po.getSupplierName())
                .eq(UtilNumber.isNotEmpty(po.getReceiptStatus()), DicSupplier::getReceiptStatus, po.getReceiptStatus())
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()),DicSupplier::getReceiptStatus, po.getReceiptStatusList())
                .orderByAsc(DicSupplier::getSupplierCode);
        dicSupplierDataWrap.page(page, queryWrapper);
        List<DicSupplierVO> list = UtilCollection.toList(page.getRecords(), DicSupplierVO.class);
        // 分页结果信息放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(list, page.getTotal()));
    }

    /**
     * 详情
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取详情
        DicSupplier DicSupplier = dicSupplierDataWrap.getById(headId);
        DicSupplierDTO dto = UtilBean.newInstance(DicSupplier, DicSupplierDTO.class);
        dataFillService.fillAttr(dto);
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(dto.getReceiptStatus())) {
            // 草稿 -【保存、提交】
            buttonVO.setButtonSave(true).setButtonSubmit(true);

        } else if (EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(dto.getReceiptStatus())) {
            // 审批中 -【撤销】
            buttonVO.setButtonRevoke(true);

        } else if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(dto.getReceiptStatus())) {
            // 未同步 -【过账】
            buttonVO.setButtonPost(true);

        } else if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(dto.getReceiptStatus())) {
            // 已驳回 -【提交】
            buttonVO.setButtonSubmit(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_DISABLED.getValue().equals(dto.getReceiptStatus())) {
            // 已停用 -【提交】
            buttonVO.setButtonSubmit(true);
        }
        // 设置详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(dto, new ExtendVO(), buttonVO));
    }

    /**
     * 开启审批流
     */
    public void setExtendWf(BizContext ctx) {
        BizResultVO<DicSupplierDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setWfRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getSupplierCode());
            resultVO.getHead().setApproveList(approveList);
            if (UtilCollection.isNotEmpty(approveList)) {
                resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
            }
        }
    }

    /**
     * 撤销
     */
    public void revoke(BizContext ctx) {
        // 入参上下文
        DicSupplierDTO dto = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 删除待办:必须在审批撤销前
        String id = workflowService.deleteTodo(dto.getId());
        // 审批撤销
        RevokeDTO revokeDTO = new RevokeDTO();
        revokeDTO.setProcessInstanceId(id);
        workflowService.revoke(revokeDTO);
        // 草稿
        dto.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        dicSupplierDataWrap.updateDtoById(dto);
    }

    /**
     * 保存
     */
    public void save(BizContext ctx) {
        // 入参上下文
        DicSupplierDTO dto = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        dto.setCreateUserId(user.getId());
        dto.setModifyUserId(user.getId());
        dto.setCreateTime(null);
        dto.setModifyTime(UtilDate.getNow());
        dto.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        // 部门判断
        String deptCode = "SUP1";
        if (EnumSupplierType.SUPPLIER_TYPE_2.getValue().equals(dto.getSupplierType())) {
            deptCode = "SUP2";
        }
        DicDept dicDept = dicDeptDataWrap.getOne(new QueryWrapper<DicDept>().lambda().eq(DicDept::getDeptCode, deptCode));
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(dto.getId())) {
            // 更新
            dicSupplierDataWrap.updateDtoById(dto);
            for (DicSupplierUserRelDTO userRelDTO : dto.getUserList()) {
                SysUser sysUser = sysUserDataWrap.getById(userRelDTO.getUserId());
                if (UtilString.isNotNullOrEmpty(userRelDTO.getOldPassword())) {
                    String password = UtilMd5.getHashPassword(userRelDTO.getOldPassword(), dto.getSupplierCode());
                    if (!sysUser.getPassword().equals(password)) {
                        // 旧密码输入错误
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_OLD_PASSWORD_INPUT_ERROR);
                    }
                    if (UtilString.isNotNullOrEmpty(userRelDTO.getPassword())) {
                        // 旧密码正确, 且新密码不是空, 则更新
                        sysUser.setPassword(UtilMd5.getHashPassword(userRelDTO.getPassword(), dto.getSupplierCode()));
                    }
                }
                // 更新用户
                String userName = dto.getSupplierName();
                sysUser.setUserName(userName);
                sysUser.setCorpId(dto.getSupplierType().longValue());
                sysUser.setValidityBeginDate(dto.getStartTime());
                sysUser.setValidityEndDate(dto.getEndTime());
                sysUser.setEmail(userRelDTO.getEmail());
                sysUserDataWrap.updateById(sysUser);
                // 更新关联关系
                userRelDTO.setSupplierName(userName);
                userRelDTO.setUserCode(dto.getSupplierCode());
                userRelDTO.setUserName(userName);
                dicSupplierUserRelDataWrap.updateDtoById(userRelDTO);
                // 更新部门关系
                sysUserDeptOfficeRelDataWrap.update(new UpdateWrapper<SysUserDeptOfficeRel>().lambda().set(SysUserDeptOfficeRel::getDeptId, dicDept.getId()).eq(SysUserDeptOfficeRel::getUserId, sysUser.getId()));
                // 刷新缓存
                editCacheService.refreshSysUserCacheById(sysUser.getId());
            }

        } else {

            // 供应商名称不能重复
            DicSupplier dicSupplier = dicSupplierDataWrap.getOne(new LambdaQueryWrapper<DicSupplier>() {{
                eq(DicSupplier::getSupplierName, dto.getSupplierName());
            }}, false);
            if(UtilObject.isNotNull(dicSupplier)){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_SUPPLIER_DUPLICATE,dicSupplier.getSupplierName());
            }

            String supplierCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_SUPPLIER.getValue());
            dto.setSupplierCode(supplierCode);
            // 新增
            dicSupplierDataWrap.saveDto(dto);
            for (DicSupplierUserRelDTO userRelDTO : dto.getUserList()) {
                // 新增用户
                String userName = dto.getSupplierName();
                // 查询用户编码是否重复
                QueryWrapper<SysUser> querySysUserWrapper = new QueryWrapper<>();
                querySysUserWrapper.lambda().eq(SysUser::getUserCode, supplierCode);
                SysUser sysUser = sysUserDataWrap.getOne(querySysUserWrapper);
                // 用户编码已存在
                if (null != sysUser) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_CODE_ALREADY_EXIST);
                }
                sysUser = new SysUser();
                sysUser.setUserCode(supplierCode);
                sysUser.setUserName(userName);
                sysUser.setPassword(UtilSHA256.encrypt(supplierCode + Const.INITIAL_PASSWORD, supplierCode.toLowerCase()));
                sysUser.setEmail(userRelDTO.getEmail());
                sysUser.setCorpId(dto.getSupplierType().longValue());
                sysUser.setUserType(EnumUserType.OUTSIDE_SUPPLIER_USER.getValue());
                sysUser.setValidityBeginDate(dto.getStartTime());
                sysUser.setValidityEndDate(dto.getEndTime());
                sysUserDataWrap.save(sysUser);
                // 新增关联关系
                userRelDTO.setSupplierId(dto.getId());
                userRelDTO.setSupplierCode(dto.getSupplierCode());
                userRelDTO.setSupplierName(userName);
                userRelDTO.setUserId(sysUser.getId());
                userRelDTO.setUserCode(supplierCode);
                userRelDTO.setUserName(userName);
                dicSupplierUserRelDataWrap.saveDto(userRelDTO);
                // 新增部门关联关系
                SysUserDeptOfficeRel sysUserDeptOfficeRel = new SysUserDeptOfficeRel();
                sysUserDeptOfficeRel.setUserId(sysUser.getId());
                sysUserDeptOfficeRel.setDeptId(dicDept.getId());
                sysUserDeptOfficeRel.setOfficeId(0L);
                sysUserDeptOfficeRel.setJobLevel(0);
                sysUserDeptOfficeRelDataWrap.save(sysUserDeptOfficeRel);
                // 刷新缓存
                editCacheService.refreshSysUserCacheById(sysUser.getId());
            }
        }
        // 营业执照保存
        receiptAttachmentService.saveBizReceiptAttachment(dto.getFileList(), dto.getId(), 1, user.getId());
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, dto.getId());
    }

    /**
     * 发起审批
     */
    public void startWorkFlow(BizContext ctx) {
        // 入参上下文
        DicSupplierDTO dto = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 发起审批
        Long receiptId = dto.getId();
        String receiptCode = dto.getSupplierCode();
        Integer receiptType = 1;
        // 经营管理部
        String deptCode = EnumDept.BMD.getCode();
        List<String> userList1 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_2);
        if (UtilCollection.isEmpty(userList1)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, EnumUserJob.LEVEL_1_APPROVAL.getValue().toString());
        }
        List<String> userList2 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_3);
        if (UtilCollection.isEmpty(userList2)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, EnumUserJob.LEVEL_2_APPROVAL.getValue().toString());
        }
        Map<String, Object> variables = new HashMap<>();
        variables.put("deptCode", deptCode);
        DicDept dicDept = dicDeptDataWrap.getOne(new QueryWrapper<DicDept>().lambda().eq(DicDept::getDeptCode, deptCode));
        // [31232]【OA待办】: 供应商管理待办展示：“请审批[XXXX公司+XX部门]创建人名称提交的流程：供应商名称”；XXX公司 取单据创建人的所属公司，XXX部门取创建人的部门 ，供应商名称取单据详情页的“供应商名称”
        variables.put("subject", "请审批[" + dictionaryService.getCorpCacheById(ctx.getCurrentUser().getCorpId()).getCorpName() + dicDept.getDeptName() + "]" + ctx.getCurrentUser().getUserName() + "提交的流程：" + dto.getSupplierName());

        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, dto.getSupplierName());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);
        // 审批中
        dto.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
        dicSupplierDataWrap.updateDtoById(dto);
    }

    /**
     * 审批回调处理
     */
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        // 获取详情
        DicSupplier head = dicSupplierDataWrap.getById(wfReceiptCo.getReceiptHeadId());

        // 审批通过
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            // 当供应商类型为“华信资源有限责任公司”时, 调用sap接口
            if (UtilConst.getInstance().isErpSyncMode() && head.getSupplierType().equals(EnumSupplierType.SUPPLIER_TYPE_1.getValue())) {
                // 未同步
                updateReceiptStatus(head, EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
                // 调用SAP
                DicSupplierDTO dto = UtilBean.newInstance(head, DicSupplierDTO.class);
                try {
                    hXSapIntegerfaceService.synSupplierInfo(dto);
                } catch (Exception e) {
                    // MQ异常不处理, 单据显示未同步即可
                    return;
                }
            }

            // 已启用
            updateReceiptStatus(head, EnumReceiptStatus.RECEIPT_STATUS_ENABLED.getValue());

            // 审批通过后 配置用户默认角色:供应商
            SysRole sysRole = sysRoleDataWrap.getOne(new QueryWrapper<SysRole>().lambda().eq(SysRole::getRoleCode, Const.JS05_ROLE_CODE));
            // 对应用户
            List<DicSupplierUserRel> list = dicSupplierUserRelDataWrap.list(new QueryWrapper<DicSupplierUserRel>().lambda().in(DicSupplierUserRel::getSupplierId, head.getId()));
            List<Long> userIds = list.stream().map(o -> o.getUserId()).collect(Collectors.toList());
            for (Long userId : userIds) {
                SysUserRoleRel sysUserRoleRel = new SysUserRoleRel();
                sysUserRoleRel.setUserId(userId);
                sysUserRoleRel.setRoleId(sysRole.getId());
                sysUserRoleRel.setRoleCode(sysRole.getRoleCode());
                sysUserRoleRelDataWrap.save(sysUserRoleRel);
            }

        } else {
            // 已驳回
            updateReceiptStatus(head, EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
        }
    }

    /**
     * 过账
     */
    public void post(BizContext ctx) {
        // 入参上下文
        DicSupplierDTO dto = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        if(EnumSupplierType.SUPPLIER_TYPE_1.getValue().equals(dto.getSupplierType())){
            //仅华信调用sap接口
            hXSapIntegerfaceService.synSupplierInfo(dto);
        }

        // 已启用
        dto.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_ENABLED.getValue());
        dicSupplierDataWrap.updateDtoById(dto);

        // 审批通过后 配置用户默认角色:供应商
        SysRole sysRole = sysRoleDataWrap.getOne(new QueryWrapper<SysRole>().lambda().eq(SysRole::getRoleCode, Const.JS05_ROLE_CODE));
        // 对应用户
        List<DicSupplierUserRel> list = dicSupplierUserRelDataWrap.list(new QueryWrapper<DicSupplierUserRel>().lambda().in(DicSupplierUserRel::getSupplierId, dto.getId()));
        List<Long> userIds = list.stream().map(o -> o.getUserId()).collect(Collectors.toList());

        if (UtilCollection.isEmpty(userIds)){
            return;
        }

        QueryWrapper<SysUserRoleRel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysUserRoleRel::getRoleId,sysRole.getId());
        queryWrapper.lambda().in(SysUserRoleRel::getUserId,userIds);
        List<SysUserRoleRel> userRoleRelList = sysUserRoleRelDataWrap.list(queryWrapper);
        Map<String, List<SysUserRoleRel>> userRoleRelListMap = userRoleRelList.stream().collect(Collectors.groupingBy(e -> e.getUserId() + "-" + e.getRoleId()));

        for (Long userId : userIds) {
            String key = userId + "-" + sysRole.getId();
            if (userRoleRelListMap.containsKey(key)){
                continue;
            }
            SysUserRoleRel sysUserRoleRel = new SysUserRoleRel();
            sysUserRoleRel.setUserId(userId);
            sysUserRoleRel.setRoleId(sysRole.getId());
            sysUserRoleRel.setRoleCode(sysRole.getRoleCode());
            sysUserRoleRelDataWrap.save(sysUserRoleRel);
        }
    }

    /**
     * 更新状态
     */
    private void updateReceiptStatus(DicSupplier head, Integer receiptStatus) {
        head.setReceiptStatus(receiptStatus);
        dicSupplierDataWrap.updateById(head);
    }

    /**
     * 删除
     */
    public void delete(BizContext ctx) {
        List<Long> ids = ctx.getContextData(Const.BIZ_CONTEXT_KEY_IDS);
        List<DicSupplier> supplierList = dicSupplierDataWrap.listByIds(ids);
        for (DicSupplier dicSupplier : supplierList) {
            if (!dicSupplier.getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_DELETE);
            }
        }
        int i = (int)bizReceiptContractHeadDataWrap.count(new QueryWrapper<BizReceiptContractHead>().lambda()
                .eq(BizReceiptContractHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_EXECUTING.getValue())
                .eq(BizReceiptContractHead::getIsDelete, 0)
                .in(BizReceiptContractHead::getSupplierId, ids));
        if (i > 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_OPERATION);
        }
        List<DicSupplierUserRel> list = dicSupplierUserRelDataWrap.list(new QueryWrapper<DicSupplierUserRel>().lambda().in(DicSupplierUserRel::getSupplierId, ids));
        // 对应用户删除
        List<Long> userIds = list.stream().map(o -> o.getUserId()).collect(Collectors.toList());
        sysUserDataWrap.removeByIds(userIds);
        // 关联关系删除
        List<Long> relIds = list.stream().map(o -> o.getId()).collect(Collectors.toList());
        dicSupplierUserRelDataWrap.removeByIds(relIds);
        // 供应商主数据删除
        dicSupplierDataWrap.removeByIds(ids);
    }

    /**
     * 启用 : 只有停用才能启用
     */
    public void enabled(BizContext ctx) {
        List<Long> ids = ctx.getContextData(Const.BIZ_CONTEXT_KEY_IDS);
        UpdateWrapper<DicSupplier> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(DicSupplier::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_ENABLED.getValue())
                .eq(DicSupplier::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_DISABLED.getValue())
                .in(DicSupplier::getId, ids);
        dicSupplierDataWrap.update(updateWrapper);
        // 对应的用户管理中的账户需要自动解冻
        List<String> codeList = dicSupplierDataWrap.listByIds(ids).stream().map(o -> o.getSupplierCode()).collect(Collectors.toList());
        UpdateWrapper<SysUser> userUpdateWrapper = new UpdateWrapper<>();
        userUpdateWrapper.lambda().in(SysUser::getUserCode, codeList).set(SysUser::getModifyUserId, ctx.getCurrentUser().getId()).set(SysUser::getIsFreeze, 0);
        sysUserDataWrap.update(userUpdateWrapper);
    }

    /**
     * 停用 : 只有启用才能停用
     */
    public void disabled(BizContext ctx) {
        List<Long> ids = ctx.getContextData(Const.BIZ_CONTEXT_KEY_IDS);
//        int i = bizReceiptContractHeadDataWrap.count(new QueryWrapper<BizReceiptContractHead>().lambda()
//                .eq(BizReceiptContractHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_EXECUTING.getValue())
//                .eq(BizReceiptContractHead::getIsDelete, 0)
//                .in(BizReceiptContractHead::getSupplierId, ids));
//        // 停用后不影响合同与框架协议单据 因此去掉校验
//        if (i > 0) {
//            throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_OPERATION);
//        }
        UpdateWrapper<DicSupplier> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(DicSupplier::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_DISABLED.getValue())
                .eq(DicSupplier::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_ENABLED.getValue())
                .in(DicSupplier::getId, ids);
        dicSupplierDataWrap.update(updateWrapper);
        // 对应的用户管理中的账户需要自动冻结
        List<String> codeList = dicSupplierDataWrap.listByIds(ids).stream().map(o -> o.getSupplierCode()).collect(Collectors.toList());
        UpdateWrapper<SysUser> userUpdateWrapper = new UpdateWrapper<>();
        userUpdateWrapper.lambda().in(SysUser::getUserCode, codeList).set(SysUser::getModifyUserId, ctx.getCurrentUser().getId()).set(SysUser::getIsFreeze, 1);
        sysUserDataWrap.update(userUpdateWrapper);
    }

    /**
     * 导入
     */
    public void importExcel(BizContext ctx) {
        //获取Excel附件
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);
        CurrentUser currentUser = ctx.getCurrentUser();
        List<DicSupplierExcelDTO> excelList;
        try {
            //获取EXCEL数据
            excelList = (List<DicSupplierExcelDTO>) UtilExcel.readExcelData(file.getInputStream(), DicSupplierExcelDTO.class);
            for (DicSupplierExcelDTO excelDTO : excelList) {
                if (UtilString.isNullOrEmpty(excelDTO.getSupplierName())) {
                    return;
                }
            }
        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
        if (UtilCollection.isEmpty(excelList)) {
            return;
        }
        // 统计个数-供应商名称
        Map<String, Long> countMap = excelList.stream().filter(e -> UtilString.isNotNullOrEmpty(e.getSupplierName())).collect(Collectors.groupingBy(DicSupplierExcelDTO::getSupplierName, Collectors.counting()));
        // 导入文件中重复的code
        List<String> repeatCodeList = countMap.entrySet().stream().filter(entry -> entry.getValue() > 1L).map(Map.Entry::getKey).collect(Collectors.toList());
        if (UtilCollection.isNotEmpty(repeatCodeList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_SUPPLIER_DIFFERENCE);
        }
        // 统计个数-纳税人识别号
        countMap = excelList.stream().filter(e -> UtilString.isNotNullOrEmpty(e.getCreditCode())).collect(Collectors.groupingBy(DicSupplierExcelDTO::getCreditCode, Collectors.counting()));
        // 导入文件中重复的code
        repeatCodeList = countMap.entrySet().stream().filter(entry -> entry.getValue() > 1L).map(Map.Entry::getKey).collect(Collectors.toList());
        if (UtilCollection.isNotEmpty(repeatCodeList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_SUPPLIER_DIFFERENCE);
        }

        String langCode = this.getLangCodeFromRequest();
        // 供应商类型
        List<SupplierTypeMapVO> supplierTypeList = EnumSupplierType.toList();
        for (SupplierTypeMapVO mapVO : supplierTypeList) {
            mapVO.setSupplierTypeI18n(i18nTextCommonService.getNameMessage(langCode, "supplierType", mapVO.getSupplierType().toString()));
        }
        // 供应商分类
        List<SupplierClassMapVO> supplierClassList = EnumSupplierClass.toList();
        for (SupplierClassMapVO mapVO : supplierClassList) {
            mapVO.setSupplierClassI18n(i18nTextCommonService.getNameMessage(langCode, "supplierClass", mapVO.getSupplierClass().toString()));
        }
        // 供应商属性
        List<SupplierAttrMapVO> supplierAttrList = EnumSupplierAttr.toList();
        for (SupplierAttrMapVO mapVO : supplierAttrList) {
            mapVO.setSupplierAttrI18n(i18nTextCommonService.getNameMessage(langCode, "supplierAttr", mapVO.getSupplierAttr().toString()));
        }
        // 业务范围
        List<ScopeMapVO> scopeList = EnumScope.toList();
        for (ScopeMapVO mapVO : scopeList) {
            mapVO.setScopeI18n(i18nTextCommonService.getNameMessage(langCode, "scope", mapVO.getScope().toString()));
        }

        List<DicSupplierDTO> supplierList = new ArrayList<>();
        List<DicSupplierUserRelDTO> userAllList = new ArrayList<>();
        for (DicSupplierExcelDTO excelDTO : excelList) {
            DicSupplierDTO dto = UtilBean.newInstance(excelDTO, DicSupplierDTO.class);
            String supplierCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_SUPPLIER.getValue());
            dto.setSupplierCode(supplierCode);
            dto.setCreateUserId(currentUser.getId());
            dto.setCreateTime(new Date());
            dto.setModifyUserId(currentUser.getId());
            dto.setModifyTime(new Date());
            // 供应商状态:默认草稿
            dto.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            // 供应商类型
            for (SupplierTypeMapVO mapVO : supplierTypeList) {
                if (mapVO.getSupplierTypeI18n().equals(dto.getSupplierTypeI18n())) {
                    dto.setSupplierType(mapVO.getSupplierType());
                }
            }
            // 供应商分类
            for (SupplierClassMapVO mapVO : supplierClassList) {
                if (mapVO.getSupplierClassI18n().equals(dto.getSupplierClassI18n())) {
                    dto.setSupplierClass(mapVO.getSupplierClass());
                }
            }
            // 供应商属性
            String[] supplierAttrI18nList = excelDTO.getSupplierAttrI18n().split(Const.COMMA);
            List<String> supplierAttr = new ArrayList<>(supplierAttrI18nList.length);
            for (SupplierAttrMapVO mapVO : supplierAttrList) {
                for (String supplierAttrI18n : supplierAttrI18nList) {
                    if (mapVO.getSupplierAttrI18n().equals(supplierAttrI18n)) {
                        supplierAttr.add(mapVO.getSupplierAttr().toString());
                    }
                }
            }
            dto.setSupplierAttr(String.join(Const.COMMA, supplierAttr));
            // 业务范围
            String[] scopeI18nList = excelDTO.getScopeI18n().split(Const.COMMA);
            List<String> scope = new ArrayList<>(scopeI18nList.length);
            for (ScopeMapVO mapVO : scopeList) {
                for (String scopeI18n : scopeI18nList) {
                    if (mapVO.getScopeI18n().equals(scopeI18n)) {
                        scope.add(mapVO.getScope().toString());
                    }
                }
            }
            dto.setScope(String.join(Const.COMMA, scope));

            String supplierName = dto.getSupplierName();
            String email = excelDTO.getEmail();

            List<DicSupplierUserRelDTO> userList = new ArrayList<>();
            DicSupplierUserRelDTO userRelDTO = new DicSupplierUserRelDTO();
            SysUser SysUser = new SysUser();
            SysUser.setUserCode(supplierCode);
            SysUser.setUserName(supplierName);
            SysUser.setEmail(email);
            userRelDTO.setSysUser(SysUser);
            userRelDTO.setSupplierCode(supplierCode);
            userRelDTO.setSupplierName(supplierName);
            userRelDTO.setUserCode(supplierCode);
            userRelDTO.setUserName(supplierName);
            userList.add(userRelDTO);

            dto.setUserList(userList);
            userAllList.addAll(userList);
            supplierList.add(dto);
        }

        // 供应商批量新增
        dicSupplierDataWrap.saveBatchDto(supplierList);

        // 用户批量新增
        List<SysUser> sysUserList = userAllList.stream().map(o -> o.getSysUser()).collect(Collectors.toList());
        sysUserDataWrap.saveBatch(sysUserList);
        // 刷新缓存
        sysUserList.forEach(o -> editCacheService.refreshSysUserCacheById(o.getId()));

        // 关联关系批量新增
        for (DicSupplierDTO supplierDTO : supplierList) {
            supplierDTO.getUserList().forEach(o -> o.setSupplierId(supplierDTO.getId()).setUserId(o.getSysUser().getId()));
        }
        dicSupplierUserRelDataWrap.saveBatchDto(userAllList);

    }

    /**
     * 导出
     */
    public void exportExcel(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("供应商导出"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);
        this.getPage(ctx);
        PageObjectVO<DicSupplierVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        List<DicSupplierDTO> list = UtilCollection.toList(vo.getResultList(), DicSupplierDTO.class);
        dataFillService.fillAttr(list);
        List<DicSupplierExcelDTO> excelDTOList = new ArrayList<>();
        String langCode = this.getLangCodeFromRequest();
        for (DicSupplierDTO dto : list) {
            DicSupplierExcelDTO exportVO = UtilBean.newInstance(dto, DicSupplierExcelDTO.class);
            exportVO.setSupplierTypeI18n(i18nTextCommonService.getNameMessage(langCode, "supplierType", dto.getSupplierType().toString()));
            if (UtilNumber.isNotEmpty(dto.getSupplierClass())) {
                exportVO.setSupplierClassI18n(i18nTextCommonService.getNameMessage(langCode, "supplierClass", dto.getSupplierClass().toString()));
            }
            if (UtilString.isNotNullOrEmpty(dto.getSupplierAttr())) {
                String[] supplierAttrList = dto.getSupplierAttr().split(Const.COMMA);
                List<String> supplierAttrI18n = new ArrayList<>(supplierAttrList.length);
                for (String supplierAttr : supplierAttrList) {
                    supplierAttrI18n.add(i18nTextCommonService.getNameMessage(langCode, "supplierAttr", supplierAttr));
                }
                exportVO.setSupplierAttrI18n(String.join(Const.COMMA, supplierAttrI18n));
            }
            if (UtilString.isNotNullOrEmpty(dto.getScope())) {
                String[] scopeList = dto.getScope().split(Const.COMMA);
                List<String> scopeI18n = new ArrayList<>(scopeList.length);
                for (String supplierAttr : scopeList) {
                    scopeI18n.add(i18nTextCommonService.getNameMessage(langCode, "scope", supplierAttr));
                }
                exportVO.setScopeI18n(String.join(Const.COMMA, scopeI18n));
            }
            // 物料等级
            if(UtilString.isNotNullOrEmpty(dto.getMatLevel())){
                String[] matTypeStrList = dto.getMatLevel().split(Const.COMMA);
                List<String> matLevelList = new ArrayList<>(matTypeStrList.length);
                for (String matLevel : matTypeStrList) {
                    matLevelList.add(i18nTextCommonService.getNameMessage(langCode, "matLevel", matLevel));
                }
                exportVO.setMatLevel(String.join(Const.COMMA, matLevelList));
            }

            if (UtilCollection.isNotEmpty(dto.getUserList())) {
                exportVO.setEmail(dto.getUserList().get(0).getEmail());
            }
            excelDTOList.add(exportVO);
        }

        UtilExcel.writeExcel(DicSupplierExcelDTO.class, excelDTOList, bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    private String getLangCodeFromRequest() {
        ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (ra == null) {
            return Const.DEFAULT_LANG_CODE;
        }
        HttpServletRequest request = ra.getRequest();
        String langCode = request.getHeader(Const.LANG_CODE_HEADER_NAME);
        return langCode == null ? Const.DEFAULT_LANG_CODE : langCode;
    }

    private String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    private String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());
        return fileName + "-" + yyyyMmDd;
    }

    /**
     * 去掉SAP编码前置的0
     * @param sapCode 原始SAP编码，如"**********"
     * @return 去掉前置0的SAP编码，如"600277"
     */
    private String removeSapCodeLeadingZeros(String sapCode) {
        if (UtilString.isNullOrEmpty(sapCode)) {
            return sapCode;
        }
        // 去掉前置的0，但保留至少一位数字
        String result = sapCode.replaceFirst("^0+", "");
        return result.isEmpty() ? "0" : result;
    }

    public void syncSupplierInfo() {
//        String response = srmService.getCall(syncSupplierUrl);
        String response = "[{\"sap\": [{\"id\": \"8536e387677b43c6beebae316996aa97\",\"compId\": \"5d915c196aab400faf83fe5d05bb566b\",\"compName\": \"RUNTO INTERNATIONAL TRADING  （PRIVATE） LIMITED\",\"purchaserId\": \"BUYER0000000145\",\"purchaserIds\": null,\"purchaserName\": \"华信资源有限责任公司\",\"purchaseCount\": null,\"sapCode\": \"**********\",\"sortCode\": \"010\",\"sortName\": \"Purchase order no.\",\"subjectCode\": \"**********\",\"subjectName\": \"应付帐款-一般应付款\",\"clauseCode\": \"F090\",\"clauseName\": \"90 天之内 到期净值\",\"currencyCode\": \"CNY\",\"currencyName\": \"人民币\",\"accountGroupCode\": \"Y100\",\"accountGroupName\": \"一般供应商\",\"address\": \"Flat No 04, Ground Floor, Rose Tower, Green Avenue, Princess Street, Park Road, Islamabad\",\"country\": \"PK\",\"language\": null,\"status\": \"SYNCED\",\"statusName\": \"已同步\",\"frozenOrganization\": false,\"freezeOrganizationRemark\": null,\"frozenCompany\": false,\"freezeCompanyRemark\": null,\"deleteCompany\": false,\"deleteOrganization\": false,\"purorgCode\": \"1104\",\"purorgName\": \"华信资源有限责任公司采购组织\",\"bstaeCode\": null,\"mindk\": null,\"frozen\": false,\"frozenCauseList\": null,\"frozenCompanyCauseList\": null,\"purchaserOrgs\": null,\"orgs\": null,\"supplierNames\": null,\"sapCodes\": null,\"lastModifiedDate\": \"2025-07-30 15:29:26\",\"lastSyncFailedInfo\": null,\"created\": null,\"purchase\": null,\"purchaserCode\": \"1104\"}],\"enterprise\": {\"id\": \"46b413247cc24ca19af99ea5b2b3f206\",\"fullName\": \"RUNTO INTERNATIONAL TRADING  （PRIVATE） LIMITED\",\"shortName\": \"Runto\",\"desc\": null,\"logo\": null,\"code\": \"Fu6hPupGyUmIODdgct\",\"activated\": true,\"country\": \"PK\",\"province\": null,\"address\": \"Flat No 04, Ground Floor, Rose Tower, Green Avenue, Princess Street, Park Road, Islamabad\",\"legalRepresentative\": \"Yan Tingju\",\"enterpriseType\": null,\"registrationInstitution\": null,\"registrationStatus\": null,\"operateStatus\": null,\"businessScope\": \"Trade agent: Acting as an agent for foreign suppliers or buyers, facilitating transactions within or outside Pakistan and earning commissions. \\nSupply chain management: providing integrated services including procurement, logistics (sea, air, and land transportation), customs clearance, warehousing, and distribution. \\nMarket consulting: Providing clients with information consulting services on import and export policies, regulations, demands, etc. of specific markets. \\nInternational procurement: Searching for and purchasing specific goods globally according to customer requirements.\",\"establishmentDate\": \"2025-03-20T16:00:00.000Z\",\"issueDate\": null,\"registeredCapital\": \"100000\",\"prefix\": \"￥\",\"validityFrom\": null,\"validityTo\": null,\"updateTime\": \"2025-07-03\",\"createTime\": \"2025-07-01\",\"postcode\": \"44000\",\"phone\": \"+92 3708229006\",\"fax\": null,\"chineseName\": \"润通国际贸易（私人）有限公司\",\"compId\": \"5d915c196aab400faf83fe5d05bb566b\",\"syncInfo\": null,\"industryphyName\": null,\"industrycoName\": null,\"certificationFlag\": false,\"certificationType\": 0,\"memberFlag\": null,\"memberFrom\": null,\"memberTo\": null,\"systemTags\": null,\"receivedCapital\": \"100000\"},\"accesses\": [{\"id\": \"630b91d9f8b54fa1b4bd926ac4e95245\",\"supplierId\": \"5d915c196aab400faf83fe5d05bb566b\",\"supplierName\": \"RUNTO INTERNATIONAL TRADING  （PRIVATE） LIMITED\",\"supplierRegisterNum\": \"Fu6hPupGyUmIODdgct\",\"purchaserId\": \"BUYER0000000145\",\"purchaserName\": \"华信资源\",\"purchaserShortName\": \"华信资源有限责任公司\",\"oaId\": \"1980893be69c192d2f5ce294470b18e0\",\"validMonths\": 36,\"projectCode\": null,\"projectName\": null,\"handler\": null,\"description\": \"后准入评价（联合体订单供货）\",\"startDate\": \"2025-07-18 08:00:00\",\"expiryDate\": \"2028-07-18 08:00:00\",\"status\": \"ACCESSED\",\"accessStatusName\": \"已准入\",\"processId\": \"a797b7066bd247a7af773a130835c0dc\",\"processType\": \"PROCESS_TYPE_ADMITTANCE_NEW\",\"processStatus\": \"COMPLETED\",\"processStatusName\": \"完成\",\"accessMaterials\": [{\"id\": \"d6f8b6f21b064ea68262186ed4283e4b\",\"material\": \"一般材料\",\"level\": \"生产性 II&III类\",\"refId\": \"d6f8b6f21b064ea68262186ed4283e4b\",\"ppqStatus\": null,\"industryStatus\": null,\"description\": null,\"descriptionMap\": null,\"category\": null,\"categoryViewDTO\": null}],\"type\": null,\"once\": false,\"hasPPQ\": null,\"processCreatedDate\": \"2025-07-14 18:55:57\",\"processUpdatedDate\": \"2025-07-14 18:55:58\",\"processCreateBy\": \"12200171\",\"processCreateByName\": \"张涛\",\"enterpriseStatus\": null,\"sapSync\": null,\"purchasePurchaserDTOList\": null,\"purchaseGroupDTOList\": null,\"effectiveList\": null,\"hasOnProcessingReview\": null,\"onProcessingReview\": null,\"purchaseCount\": null,\"validTo\": null}],\"users\": [{\"id\": \"22559bcedc4b430fb8f2b0b0ba477603\",\"login\": \"U1046461\",\"name\": \"Yan Tingju\",\"email\": \"<EMAIL>\",\"email2\": \"\",\"mobile\": \"+8615864239255\",\"userCategory\": 2,\"sourceFrom\": 1,\"companyId\": \"46b413247cc24ca19af99ea5b2b3f206\",\"companyOrgCode\": null,\"companyCode\": \"Fu6hPupGyUmIODdgct\",\"companyName\": null,\"deptCode\": null,\"deptName\": null,\"position\": null,\"wechatUnionId\": null,\"wcmpOpenId\": null,\"wcmpFlag\": null,\"wcmpOn\": null,\"wcppOpenId\": null,\"wcppFlag\": null,\"wcppOn\": null,\"activated\": true,\"enableFlag\": true,\"leaveFlag\": false,\"mainFlag\": true,\"deleteFlag\": false,\"disableBy\": null,\"disableByName\": null,\"disableByCompanyCode\": null,\"disableByCompanyName\": null,\"disableCause\": null,\"createdBy\": \"SYSTEM\",\"createdOn\": \"2025-07-01T06:31:41.000+00:00\",\"updatedBy\": \"SYSTEM\",\"updatedOn\": \"2025-07-02T08:04:43.000+00:00\",\"expiredDate\": \"2025-12-27T16:00:00.000+00:00\",\"mobileFlag\": true,\"emailFlag\": false,\"sourceFromEnum\": \"SEC\"}]}]";
        log.info("同步供应商信息开始{}", response);
        if (UtilString.isNotNullOrEmpty(response)) {
            ObjectMapper mapper = new ObjectMapper();
            try {
                JsonNode rootNode = mapper.readTree(response);
                if (rootNode.isArray()) {
                    for (JsonNode itemNode : rootNode) {
                        // sap 编码
                        JsonNode sapNode = itemNode.get("sap");
                        // 供应商名称
                        String compName = "";
                        // 主账号
                        String account = "";
                        // 法人
                        String legalPerson = "";
                        // 地址
                        String address = "";
                        // 国家
                        String country = "";

                        // if (sapNode != null && sapNode.isArray() && !sapNode.isEmpty()) {
                        //     compName = sapNode.get(0).get("compName").asText("");
                        // }
                        JsonNode enterpriseNode = itemNode.get("enterprise");
                        if (enterpriseNode != null) {
                            legalPerson = enterpriseNode.get("legalRepresentative").asText();
                            address = enterpriseNode.get("address").asText();
                            country = enterpriseNode.get("country").asText();
                            compName = enterpriseNode.get("fullName").asText();
                        }
                        JsonNode usersNode = itemNode.get("users");
                        if (usersNode != null && usersNode.isArray()) {
                            for (JsonNode jsonNode : usersNode) {
                                if (jsonNode.get("mainFlag").asBoolean()) {
                                    account = jsonNode.get("login").asText("");
                                    break;
                                }
                            }
                        }

                        JsonNode accessesNode = itemNode.get("accesses");
                        Set<String> set = new HashSet<>();
                        for (JsonNode jsonNode : accessesNode) {
                            set.add(jsonNode.get("purchaserName").asText(""));

                        }
                        if (accessesNode.isArray()) {
                            if (set.size() == 1) {
                                if (set.stream().allMatch(name -> name.startsWith("华信资源"))) {
                                    handleSingleSupplier(accessesNode, sapNode, account, compName, legalPerson, address, country, EnumSupplierType.SUPPLIER_TYPE_1);
                                } else if (set.stream().allMatch(name -> name.startsWith("上海能殷"))) {
                                    handleSingleSupplier(accessesNode, sapNode, account, compName, legalPerson, address, country, EnumSupplierType.SUPPLIER_TYPE_2);
                                }
                            } else if (set.size() == 2) {

                                handleDualSuppliers(accessesNode, sapNode, account, compName, legalPerson, address, country);


                            }
                        }
                    }
                }
            } catch (JsonProcessingException e) {
                throw new RuntimeException("JSON解析失败", e);
            }
        }
    }

    private void handleSingleSupplier(JsonNode accessesNode, JsonNode sapNode, String account, String compName, String legalPerson, String address, String country, EnumSupplierType supplierType) {
        String sapCode = "";
        for (JsonNode jsonNode : sapNode) {
            String rawSapCode = jsonNode.get("sapCode").asText();
            if (UtilString.isNotNullOrEmpty(rawSapCode)) {
                sapCode = removeSapCodeLeadingZeros(rawSapCode);
                break;
            }
        }

        // 1、当供应商名称所属企业/公司仅为华信时；
        //      1.1 WMS没有当前供应商编码，则WMS新增此供应商，并记录当前用户的主U账号、统一社会性代码、供应商编码、供应商名称、等，并根据现有逻辑自动生成供应商的账号；
        // 2、当供应商名称所属企业/公司仅为能殷时；
        //      2.1 WMS没有当前供应商名称，则WMS新增此供应商，并记录当前用户的主U账号、统一社会性代码、（供应商编码SRM是没有的，我们需要自动附上U账号的值）、供应商名称，并根据现有逻辑自动生成供应商的账号；
        //      2.2 WMS有当前供应商名称（则代表当前供应商的“供应商编码”为SRM主U账号或WMS自动生成的编码），则WMS只记录主U账号；
        LambdaQueryWrapper<DicSupplier> queryWrapper = new QueryWrapper<DicSupplier>().lambda();
        if (supplierType == EnumSupplierType.SUPPLIER_TYPE_1) {
            if (UtilString.isNullOrEmpty(sapCode)) {
                // 华信的供应商编码如果为空，则不处理
                return;
            }
            queryWrapper.eq(DicSupplier::getSupplierCode, sapCode);
        } else {
            queryWrapper.eq(DicSupplier::getSupplierName, compName);
        }
        DicSupplier supplier = dicSupplierDataWrap.getOne(queryWrapper);
        if (Objects.isNull(supplier)) {
            String supplierCode = supplierType == EnumSupplierType.SUPPLIER_TYPE_1 ? sapCode : account;
            createAndSaveSupplier(accessesNode, supplierCode, compName, account, legalPerson, address, country, supplierType);
        } else {
            if (!account.equals(supplier.getUAccount())) {
                supplier.setUAccount(account);
                dicSupplierDataWrap.updateById(supplier);
            }
        }
    }

    private void handleDualSuppliers(JsonNode accessesNode, JsonNode sapNode, String account, String compName, String legalPerson, String address, String country) {
        String sapCode = "";
        for (JsonNode node : sapNode) {
            String rawSapCode = node.get("sapCode").asText();
            if (UtilString.isNotNullOrEmpty(rawSapCode)) {
                sapCode = removeSapCodeLeadingZeros(rawSapCode);
            }
        }

        DicSupplier supplier = dicSupplierDataWrap.getOne(new QueryWrapper<DicSupplier>().lambda().eq(DicSupplier::getSupplierName, compName));
        if (Objects.isNull(supplier)) {
            createAndSaveSupplier(accessesNode, sapCode, compName, account, legalPerson, address, country, EnumSupplierType.SUPPLIER_TYPE_1);
        } else {
            if (supplier.getSupplierType().equals(EnumSupplierType.SUPPLIER_TYPE_1.getValue()) && !account.equals(supplier.getUAccount())) {
                supplier.setUAccount(account);
                dicSupplierDataWrap.updateById(supplier);
            } else if (supplier.getSupplierType().equals(EnumSupplierType.SUPPLIER_TYPE_2.getValue())) {
                // 3.3当这个供应商的名称在WMS系统中存在时，且对应的供应商类型为能殷；系统需要将供应商编码（主U账号、SAP编码）替换成SRM推送过来的新SAP供应商编码，再记录下主U账号；
                // 3.4当这个供应商的名称在WMS系统中存在时，且对应的供应商类型为能殷，并且WMS存在供应商编码，则系统只需记录主U账号；
                supplier.setSupplierCode(UtilString.isNotNullOrEmpty(sapCode) ? sapCode : account);
                supplier.setUAccount(account);
                dicSupplierDataWrap.updateById(supplier);
            }
        }
    }

    private void createAndSaveSupplier(JsonNode accessesNode, String supplierCode, String compName, String account, String legalPerson, String address, String country, EnumSupplierType supplierType) {
        DicSupplierDTO dto = new DicSupplierDTO();
        dto.setSupplierCode(supplierCode);
        dto.setSupplierName(compName);
        dto.setUAccount(account);
        dto.setLegalPerson(legalPerson);
        dto.setAddress(address);
        dto.setCountry(country);
        dto.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_ENABLED.getValue());
        dto.setSupplierType(supplierType.getValue());
        dto.setCreditCode(accessesNode.get(0).get("supplierRegisterNum").asText(""));
        dicSupplierDataWrap.saveDto(dto);

        String userName = dto.getSupplierName();
        QueryWrapper<SysUser> querySysUserWrapper = new QueryWrapper<>();
        querySysUserWrapper.lambda().eq(SysUser::getUserCode, supplierCode);
        SysUser sysUser = sysUserDataWrap.getOne(querySysUserWrapper);

        if (sysUser == null) {
            sysUser = new SysUser();
            sysUser.setUserCode(supplierCode);
            sysUser.setUserName(userName);
            sysUser.setPassword(UtilSHA256.encrypt(Const.INITIAL_PASSWORD, supplierCode.toLowerCase()));
            sysUser.setUserType(EnumUserType.OUTSIDE_SUPPLIER_USER.getValue());
            sysUser.setValidityBeginDate(UtilDate.convertToDate(accessesNode.get(0).get("startDate").asText()));
            sysUser.setValidityEndDate(UtilDate.convertToDate(accessesNode.get(0).get("expiryDate").asText()));
            sysUser.setCorpId(Long.valueOf(supplierType.getValue()));
            sysUserDataWrap.save(sysUser);

            DicSupplierUserRelDTO userRelDTO = new DicSupplierUserRelDTO();
            userRelDTO.setSupplierId(dto.getId());
            userRelDTO.setSupplierCode(dto.getSupplierCode());
            userRelDTO.setSupplierName(userName);
            userRelDTO.setUserId(sysUser.getId());
            userRelDTO.setUserCode(supplierCode);
            userRelDTO.setUserName(userName);
            dicSupplierUserRelDataWrap.saveDto(userRelDTO);

            String deptCode = "SUP1";
            if (EnumSupplierType.SUPPLIER_TYPE_2.getValue().equals(dto.getSupplierType())) {
                deptCode = "SUP2";
            }

            DicDept dicDept = dicDeptDataWrap.getOne(new QueryWrapper<DicDept>().lambda().eq(DicDept::getDeptCode, deptCode));
            SysUserDeptOfficeRel sysUserDeptOfficeRel = new SysUserDeptOfficeRel();
            sysUserDeptOfficeRel.setUserId(sysUser.getId());
            sysUserDeptOfficeRel.setDeptId(dicDept.getId());
            sysUserDeptOfficeRel.setOfficeId(0L);
            sysUserDeptOfficeRel.setJobLevel(0);
            sysUserDeptOfficeRelDataWrap.save(sysUserDeptOfficeRel);

            editCacheService.refreshSysUserCacheById(sysUser.getId());
        }
    }

}
